package com.ruoyi.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.config.CacheProperties;

/**
 * 缓存Key工具类
 * 
 * <AUTHOR>
 */
@Component
public class CacheKeyUtil {

    @Autowired
    private CacheProperties cacheProperties;

    /**
     * 生成缓存Key
     * 
     * @param module 模块名
     * @param method 方法名
     * @param params 参数（可选）
     * @return 完整的缓存Key
     */
    public String generateKey(String module, String method, Object... params) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(cacheProperties.getKeyPrefix())
                  .append(module)
                  .append(":")
                  .append(method);
        
        if (params != null && params.length > 0) {
            for (Object param : params) {
                keyBuilder.append(":").append(param != null ? param.toString() : "null");
            }
        }
        
        return keyBuilder.toString();
    }

    /**
     * 生成用户相关的缓存Key
     * 
     * @param userId 用户ID
     * @param module 模块名
     * @param method 方法名
     * @param params 参数（可选）
     * @return 完整的缓存Key
     */
    public String generateUserKey(Long userId, String module, String method, Object... params) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(cacheProperties.getKeyPrefix())
                  .append("user:")
                  .append(userId)
                  .append(":")
                  .append(module)
                  .append(":")
                  .append(method);
        
        if (params != null && params.length > 0) {
            for (Object param : params) {
                keyBuilder.append(":").append(param != null ? param.toString() : "null");
            }
        }
        
        return keyBuilder.toString();
    }

    /**
     * 生成分页缓存Key
     * 
     * @param module 模块名
     * @param method 方法名
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param params 其他参数（可选）
     * @return 完整的缓存Key
     */
    public String generatePageKey(String module, String method, Integer pageNum, Integer pageSize, Object... params) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(cacheProperties.getKeyPrefix())
                  .append(module)
                  .append(":")
                  .append(method)
                  .append(":page:")
                  .append(pageNum)
                  .append(":")
                  .append(pageSize);
        
        if (params != null && params.length > 0) {
            for (Object param : params) {
                keyBuilder.append(":").append(param != null ? param.toString() : "null");
            }
        }
        
        return keyBuilder.toString();
    }
}
