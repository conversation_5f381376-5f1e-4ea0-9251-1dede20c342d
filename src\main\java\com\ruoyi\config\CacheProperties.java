package com.ruoyi.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 缓存配置属性类
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "cache.redis")
public class CacheProperties {

    /**
     * 默认缓存过期时间（秒）
     */
    private Long defaultTtl = 3600L;

    /**
     * 缓存key前缀
     */
    private String keyPrefix = "ruoyi:cache:";

    /**
     * 是否启用缓存
     */
    private Boolean enabled = true;

    public Long getDefaultTtl() {
        return defaultTtl;
    }

    public void setDefaultTtl(Long defaultTtl) {
        this.defaultTtl = defaultTtl;
    }

    public String getKeyPrefix() {
        return keyPrefix;
    }

    public void setKeyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
