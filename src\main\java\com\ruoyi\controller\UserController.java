package com.ruoyi.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.config.CacheProperties;
import com.ruoyi.service.UserService;
import com.ruoyi.util.CacheKeyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户控制器 - 带Redis缓存功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CacheKeyUtil cacheKeyUtil;

    @Autowired
    private CacheProperties cacheProperties;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 获取用户列表（带缓存）
     * 
     * @return 用户列表
     */
    @GetMapping("/list")
    public List<Map<String, Object>> getUserList() {
        // 检查缓存是否启用
        if (!cacheProperties.getEnabled()) {
            logger.info("缓存未启用，直接查询数据库");
            return userService.getUserList();
        }

        // 生成缓存Key
        String cacheKey = cacheKeyUtil.generateKey("user", "list");
        logger.info("查询用户列表，缓存Key: {}", cacheKey);

        try {
            // 先从缓存中获取数据
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                logger.info("从缓存中获取到数据，Key: {}", cacheKey);
                // 将缓存数据转换为List<Map<String, Object>>
                List<Map<String, Object>> result = objectMapper.convertValue(
                    cachedData, new TypeReference<List<Map<String, Object>>>() {});
                return result;
            }

            // 缓存中没有数据，从业务逻辑获取
            logger.info("缓存中没有数据，从数据库查询，Key: {}", cacheKey);
            List<Map<String, Object>> data = userService.getUserList();

            // 将数据存入缓存
            redisTemplate.opsForValue().set(cacheKey, data, cacheProperties.getDefaultTtl(), TimeUnit.SECONDS);
            logger.info("数据已存入缓存，Key: {}, TTL: {}秒", cacheKey, cacheProperties.getDefaultTtl());

            return data;

        } catch (Exception e) {
            logger.error("缓存操作异常，Key: {}, 错误信息: {}", cacheKey, e.getMessage(), e);
            // 缓存异常时，直接查询数据库
            return userService.getUserList();
        }
    }

    /**
     * 根据状态获取用户列表（带缓存）
     * 
     * @param status 用户状态
     * @return 用户列表
     */
    @GetMapping("/list/status/{status}")
    public List<Map<String, Object>> getUserListByStatus(@PathVariable String status) {
        if (!cacheProperties.getEnabled()) {
            return userService.getUserListByStatus(status);
        }

        String cacheKey = cacheKeyUtil.generateKey("user", "listByStatus", status);
        logger.info("根据状态查询用户列表，状态: {}, 缓存Key: {}", status, cacheKey);

        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                logger.info("从缓存中获取到数据，Key: {}", cacheKey);
                List<Map<String, Object>> result = objectMapper.convertValue(
                    cachedData, new TypeReference<List<Map<String, Object>>>() {});
                return result;
            }

            logger.info("缓存中没有数据，从数据库查询，Key: {}", cacheKey);
            List<Map<String, Object>> data = userService.getUserListByStatus(status);

            // 根据状态设置不同的缓存时间
            long ttl = "active".equals(status) ? cacheProperties.getDefaultTtl() : cacheProperties.getDefaultTtl() / 2;
            redisTemplate.opsForValue().set(cacheKey, data, ttl, TimeUnit.SECONDS);
            logger.info("数据已存入缓存，Key: {}, TTL: {}秒", cacheKey, ttl);

            return data;

        } catch (Exception e) {
            logger.error("缓存操作异常，Key: {}, 错误信息: {}", cacheKey, e.getMessage(), e);
            return userService.getUserListByStatus(status);
        }
    }

    /**
     * 分页获取用户列表（带缓存）
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 用户列表
     */
    @GetMapping("/list/page")
    public List<Map<String, Object>> getUserListByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        if (!cacheProperties.getEnabled()) {
            return userService.getUserListByPage(pageNum, pageSize);
        }

        String cacheKey = cacheKeyUtil.generatePageKey("user", "listByPage", pageNum, pageSize);
        logger.info("分页查询用户列表，页码: {}, 页大小: {}, 缓存Key: {}", pageNum, pageSize, cacheKey);

        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                logger.info("从缓存中获取到数据，Key: {}", cacheKey);
                List<Map<String, Object>> result = objectMapper.convertValue(
                    cachedData, new TypeReference<List<Map<String, Object>>>() {});
                return result;
            }

            logger.info("缓存中没有数据，从数据库查询，Key: {}", cacheKey);
            List<Map<String, Object>> data = userService.getUserListByPage(pageNum, pageSize);

            redisTemplate.opsForValue().set(cacheKey, data, cacheProperties.getDefaultTtl(), TimeUnit.SECONDS);
            logger.info("数据已存入缓存，Key: {}, TTL: {}秒", cacheKey, cacheProperties.getDefaultTtl());

            return data;

        } catch (Exception e) {
            logger.error("缓存操作异常，Key: {}, 错误信息: {}", cacheKey, e.getMessage(), e);
            return userService.getUserListByPage(pageNum, pageSize);
        }
    }

    /**
     * 根据用户ID获取用户详情（带缓存）
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    @GetMapping("/{userId}")
    public List<Map<String, Object>> getUserById(@PathVariable Long userId) {
        if (!cacheProperties.getEnabled()) {
            return userService.getUserById(userId);
        }

        String cacheKey = cacheKeyUtil.generateUserKey(userId, "user", "detail");
        logger.info("查询用户详情，用户ID: {}, 缓存Key: {}", userId, cacheKey);

        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                logger.info("从缓存中获取到数据，Key: {}", cacheKey);
                List<Map<String, Object>> result = objectMapper.convertValue(
                    cachedData, new TypeReference<List<Map<String, Object>>>() {});
                return result;
            }

            logger.info("缓存中没有数据，从数据库查询，Key: {}", cacheKey);
            List<Map<String, Object>> data = userService.getUserById(userId);

            // 用户详情缓存时间较长
            long ttl = cacheProperties.getDefaultTtl() * 2;
            redisTemplate.opsForValue().set(cacheKey, data, ttl, TimeUnit.SECONDS);
            logger.info("数据已存入缓存，Key: {}, TTL: {}秒", cacheKey, ttl);

            return data;

        } catch (Exception e) {
            logger.error("缓存操作异常，Key: {}, 错误信息: {}", cacheKey, e.getMessage(), e);
            return userService.getUserById(userId);
        }
    }

    /**
     * 清除指定用户的缓存
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/cache/{userId}")
    public Map<String, Object> clearUserCache(@PathVariable Long userId) {
        try {
            String userDetailKey = cacheKeyUtil.generateUserKey(userId, "user", "detail");
            Boolean deleted = redisTemplate.delete(userDetailKey);

            logger.info("清除用户缓存，用户ID: {}, 缓存Key: {}, 删除结果: {}", userId, userDetailKey, deleted);

            return Map.of(
                "success", true,
                "message", "用户缓存清除成功",
                "userId", userId,
                "deleted", deleted
            );
        } catch (Exception e) {
            logger.error("清除用户缓存异常，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            return Map.of(
                "success", false,
                "message", "用户缓存清除失败: " + e.getMessage(),
                "userId", userId
            );
        }
    }

    /**
     * 清除所有用户相关缓存
     *
     * @return 操作结果
     */
    @DeleteMapping("/cache/all")
    public Map<String, Object> clearAllUserCache() {
        try {
            String pattern = cacheProperties.getKeyPrefix() + "user:*";
            Set<String> keys = redisTemplate.keys(pattern);

            if (keys != null && !keys.isEmpty()) {
                Long deletedCount = redisTemplate.delete(keys);
                logger.info("清除所有用户缓存，匹配模式: {}, 删除数量: {}", pattern, deletedCount);

                return Map.of(
                    "success", true,
                    "message", "所有用户缓存清除成功",
                    "deletedCount", deletedCount
                );
            } else {
                return Map.of(
                    "success", true,
                    "message", "没有找到需要清除的缓存",
                    "deletedCount", 0
                );
            }
        } catch (Exception e) {
            logger.error("清除所有用户缓存异常，错误信息: {}", e.getMessage(), e);
            return Map.of(
                "success", false,
                "message", "清除所有用户缓存失败: " + e.getMessage()
            );
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    @GetMapping("/cache/stats")
    public Map<String, Object> getCacheStats() {
        try {
            String pattern = cacheProperties.getKeyPrefix() + "user:*";
            Set<String> keys = redisTemplate.keys(pattern);

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalKeys", keys != null ? keys.size() : 0);
            stats.put("keyPrefix", cacheProperties.getKeyPrefix());
            stats.put("defaultTtl", cacheProperties.getDefaultTtl());
            stats.put("cacheEnabled", cacheProperties.getEnabled());

            if (keys != null && !keys.isEmpty()) {
                List<Map<String, Object>> keyDetails = new ArrayList<>();
                for (String key : keys) {
                    Map<String, Object> keyInfo = new HashMap<>();
                    keyInfo.put("key", key);
                    keyInfo.put("ttl", redisTemplate.getExpire(key, TimeUnit.SECONDS));
                    keyInfo.put("type", redisTemplate.type(key).name());
                    keyDetails.add(keyInfo);
                }
                stats.put("keys", keyDetails);
            }

            return stats;
        } catch (Exception e) {
            logger.error("获取缓存统计信息异常，错误信息: {}", e.getMessage(), e);
            return Map.of(
                "success", false,
                "message", "获取缓存统计信息失败: " + e.getMessage()
            );
        }
    }
}
