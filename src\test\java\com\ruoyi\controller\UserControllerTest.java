package com.ruoyi.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.config.CacheProperties;
import com.ruoyi.service.UserService;
import com.ruoyi.util.RedisCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户控制器测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureWebMvc
public class UserControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private UserService userService;

    @Autowired
    private CacheProperties cacheProperties;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 默认启用缓存
        cacheProperties.setEnabled(true);
    }

    @Test
    void testGetUserListWithCache() throws Exception {
        // 模拟缓存为空
        when(redisTemplate.opsForValue().get(anyString())).thenReturn(null);
        
        // 执行请求
        mockMvc.perform(get("/api/users/list"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(10));
        
        // 验证缓存设置被调用
        verify(redisTemplate.opsForValue()).set(anyString(), any(), anyLong(), any());
    }

    @Test
    void testGetUserListFromCache() throws Exception {
        // 准备缓存数据
        List<Map<String, Object>> cachedData = new ArrayList<>();
        Map<String, Object> user = new HashMap<>();
        user.put("id", 1);
        user.put("username", "cached_user");
        user.put("email", "<EMAIL>");
        cachedData.add(user);
        
        // 模拟从缓存获取数据
        when(redisTemplate.opsForValue().get(anyString())).thenReturn(cachedData);
        
        // 执行请求
        mockMvc.perform(get("/api/users/list"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].username").value("cached_user"));
        
        // 验证没有调用缓存设置（因为数据来自缓存）
        verify(redisTemplate.opsForValue(), never()).set(anyString(), any(), anyLong(), any());
    }

    @Test
    void testGetUserListByStatus() throws Exception {
        // 模拟缓存为空
        when(redisTemplate.opsForValue().get(anyString())).thenReturn(null);
        
        // 执行请求
        mockMvc.perform(get("/api/users/list/status/active"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
        
        // 验证缓存设置被调用
        verify(redisTemplate.opsForValue()).set(anyString(), any(), anyLong(), any());
    }

    @Test
    void testGetUserListByPage() throws Exception {
        // 模拟缓存为空
        when(redisTemplate.opsForValue().get(anyString())).thenReturn(null);
        
        // 执行请求
        mockMvc.perform(get("/api/users/list/page")
                .param("pageNum", "1")
                .param("pageSize", "5"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(5));
        
        // 验证缓存设置被调用
        verify(redisTemplate.opsForValue()).set(anyString(), any(), anyLong(), any());
    }

    @Test
    void testGetUserById() throws Exception {
        // 模拟缓存为空
        when(redisTemplate.opsForValue().get(anyString())).thenReturn(null);
        
        // 执行请求
        mockMvc.perform(get("/api/users/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1));
        
        // 验证缓存设置被调用
        verify(redisTemplate.opsForValue()).set(anyString(), any(), anyLong(), any());
    }

    @Test
    void testClearUserCache() throws Exception {
        // 模拟删除缓存成功
        when(redisTemplate.delete(anyString())).thenReturn(true);
        
        // 执行请求
        mockMvc.perform(delete("/api/users/cache/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.userId").value(1));
        
        // 验证删除缓存被调用
        verify(redisTemplate).delete(anyString());
    }

    @Test
    void testClearAllUserCache() throws Exception {
        // 模拟查找缓存keys
        Set<String> keys = Set.of("ruoyi:cache:user:list", "ruoyi:cache:user:detail:1");
        when(redisTemplate.keys(anyString())).thenReturn(keys);
        when(redisTemplate.delete(keys)).thenReturn(2L);
        
        // 执行请求
        mockMvc.perform(delete("/api/users/cache/all"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.deletedCount").value(2));
        
        // 验证批量删除缓存被调用
        verify(redisTemplate).delete(keys);
    }

    @Test
    void testGetCacheStats() throws Exception {
        // 模拟缓存统计数据
        Set<String> keys = Set.of("ruoyi:cache:user:list", "ruoyi:cache:user:detail:1");
        when(redisTemplate.keys(anyString())).thenReturn(keys);
        when(redisTemplate.getExpire(anyString(), any())).thenReturn(3600L);
        when(redisTemplate.type(anyString())).thenReturn(org.springframework.data.redis.connection.DataType.STRING);
        
        // 执行请求
        mockMvc.perform(get("/api/users/cache/stats"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalKeys").value(2))
                .andExpect(jsonPath("$.cacheEnabled").value(true));
    }

    @Test
    void testCacheDisabled() throws Exception {
        // 禁用缓存
        cacheProperties.setEnabled(false);
        
        // 执行请求
        mockMvc.perform(get("/api/users/list"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
        
        // 验证没有调用缓存操作
        verify(redisTemplate.opsForValue(), never()).get(anyString());
        verify(redisTemplate.opsForValue(), never()).set(anyString(), any(), anyLong(), any());
    }
}
