# Spring Boot Redis 缓存实现

这个项目演示了如何在Spring Boot应用的Controller层使用RedisTemplate实现缓存功能，专门处理`List<Map<String, Object>>`类型的数据。

## 项目特性

- ✅ 仅在Controller层实现缓存，不侵入Service层
- ✅ 专门处理`List<Map<String, Object>>`类型数据
- ✅ 使用RedisTemplate进行缓存操作
- ✅ 完善的异常处理机制
- ✅ 灵活的缓存配置
- ✅ 提供两种实现方式：直接使用RedisTemplate和使用工具类
- ✅ 支持缓存预热和统计

## 项目结构

```
src/
├── main/
│   ├── java/com/ruoyi/
│   │   ├── config/
│   │   │   ├── RedisConfig.java          # Redis配置类
│   │   │   └── CacheProperties.java      # 缓存配置属性
│   │   ├── controller/
│   │   │   ├── UserController.java       # 直接使用RedisTemplate的Controller
│   │   │   └── UserControllerSimple.java # 使用工具类的简化Controller
│   │   ├── service/
│   │   │   └── UserService.java          # 业务服务类
│   │   ├── util/
│   │   │   ├── CacheKeyUtil.java         # 缓存Key生成工具
│   │   │   └── RedisCache.java           # Redis缓存工具类
│   │   └── RedisCacheDemoApplication.java # 启动类
│   └── resources/
│       └── application.yml               # 配置文件
└── test/
    └── java/com/ruoyi/controller/
        └── UserControllerTest.java       # 测试类
```

## 核心功能

### 1. 缓存配置

在`application.yml`中配置Redis连接和缓存参数：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 3000ms

cache:
  redis:
    default-ttl: 3600        # 默认缓存过期时间（秒）
    key-prefix: "ruoyi:cache:" # 缓存key前缀
    enabled: true            # 是否启用缓存
```

### 2. 缓存实现方式

#### 方式一：直接使用RedisTemplate

```java
@GetMapping("/list")
public List<Map<String, Object>> getUserList() {
    String cacheKey = cacheKeyUtil.generateKey("user", "list");
    
    try {
        // 先从缓存获取
        Object cachedData = redisTemplate.opsForValue().get(cacheKey);
        if (cachedData != null) {
            return objectMapper.convertValue(cachedData, 
                new TypeReference<List<Map<String, Object>>>() {});
        }
        
        // 缓存不存在，查询数据库
        List<Map<String, Object>> data = userService.getUserList();
        
        // 存入缓存
        redisTemplate.opsForValue().set(cacheKey, data, 
            cacheProperties.getDefaultTtl(), TimeUnit.SECONDS);
        
        return data;
    } catch (Exception e) {
        // 异常时直接查询数据库
        return userService.getUserList();
    }
}
```

#### 方式二：使用RedisCache工具类（推荐）

```java
@GetMapping("/list")
public List<Map<String, Object>> getUserList() {
    String cacheKey = cacheKeyUtil.generateKey("user", "list");
    
    return redisCache.getOrSet(cacheKey, () -> {
        return userService.getUserList();
    });
}
```

### 3. 缓存Key命名规则

使用`CacheKeyUtil`生成标准化的缓存Key：

- 普通缓存：`ruoyi:cache:module:method:param1:param2`
- 用户相关：`ruoyi:cache:user:userId:module:method:param1`
- 分页缓存：`ruoyi:cache:module:method:page:pageNum:pageSize:param1`

### 4. 缓存管理功能

- **清除单个缓存**：`DELETE /api/users/cache/{userId}`
- **清除所有缓存**：`DELETE /api/users/cache/all`
- **缓存统计信息**：`GET /api/users/cache/stats`
- **缓存预热**：`POST /api/v2/users/cache/warmup`

## API接口

### 用户数据接口

| 方法 | 路径 | 描述 | 缓存Key示例 |
|------|------|------|-------------|
| GET | `/api/users/list` | 获取用户列表 | `ruoyi:cache:user:list` |
| GET | `/api/users/list/status/{status}` | 按状态获取用户 | `ruoyi:cache:user:listByStatus:active` |
| GET | `/api/users/list/page` | 分页获取用户 | `ruoyi:cache:user:listByPage:page:1:10` |
| GET | `/api/users/{userId}` | 获取用户详情 | `ruoyi:cache:user:1:user:detail` |

### 缓存管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| DELETE | `/api/users/cache/{userId}` | 清除指定用户缓存 |
| DELETE | `/api/users/cache/all` | 清除所有用户缓存 |
| GET | `/api/users/cache/stats` | 获取缓存统计信息 |
| POST | `/api/v2/users/cache/warmup` | 缓存预热 |

## 运行项目

### 1. 启动Redis服务

```bash
# 使用Docker启动Redis
docker run -d --name redis -p 6379:6379 redis:latest

# 或者使用本地Redis服务
redis-server
```

### 2. 运行Spring Boot应用

```bash
# 使用Maven
mvn spring-boot:run

# 或者直接运行jar包
java -jar target/redis-cache-demo-1.0.0.jar
```

### 3. 测试缓存功能

```bash
# 第一次请求（从数据库查询，较慢）
curl http://localhost:8080/api/users/list

# 第二次请求（从缓存获取，较快）
curl http://localhost:8080/api/users/list

# 查看缓存统计
curl http://localhost:8080/api/users/cache/stats

# 清除缓存
curl -X DELETE http://localhost:8080/api/users/cache/all
```

## 缓存策略

### 1. 缓存时间设置

- **用户列表**：1小时（3600秒）
- **活跃用户**：1小时，非活跃用户：30分钟
- **用户详情**：2小时（7200秒）
- **分页数据**：1小时

### 2. 异常处理

- Redis连接异常时，自动降级到数据库查询
- 序列化/反序列化异常时，记录日志并返回数据库数据
- 缓存操作异常不影响正常业务流程

### 3. 缓存更新策略

- 采用Cache-Aside模式
- 数据更新时需要手动清除相关缓存
- 支持缓存预热，提前加载热点数据

## 监控和调试

### 1. 日志配置

```yaml
logging:
  level:
    com.ruoyi: DEBUG
    org.springframework.data.redis: DEBUG
```

### 2. 缓存监控

通过`/api/users/cache/stats`接口可以查看：
- 缓存Key总数
- 每个Key的过期时间
- 缓存配置信息

## 最佳实践

1. **合理设置缓存时间**：根据数据更新频率设置TTL
2. **异常处理**：确保缓存异常不影响业务
3. **Key命名规范**：使用统一的Key命名规则
4. **缓存预热**：对于热点数据，应用启动时预先加载
5. **监控告警**：监控缓存命中率和Redis性能
6. **数据一致性**：及时清除过期缓存，保证数据一致性

## 扩展功能

- 支持分布式锁防止缓存击穿
- 支持缓存降级和熔断
- 支持多级缓存（本地缓存+Redis）
- 支持缓存预热任务调度
- 支持缓存统计和监控面板
