package com.ruoyi.controller;

import com.ruoyi.service.UserService;
import com.ruoyi.util.CacheKeyUtil;
import com.ruoyi.util.RedisCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户控制器 - 使用RedisCache工具类的简化版本
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v2/users")
public class UserControllerSimple {

    private static final Logger logger = LoggerFactory.getLogger(UserControllerSimple.class);

    @Autowired
    private UserService userService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CacheKeyUtil cacheKeyUtil;

    /**
     * 获取用户列表（使用RedisCache工具类）
     * 
     * @return 用户列表
     */
    @GetMapping("/list")
    public List<Map<String, Object>> getUserList() {
        String cacheKey = cacheKeyUtil.generateKey("user", "list");
        logger.info("查询用户列表，缓存Key: {}", cacheKey);
        
        return redisCache.getOrSet(cacheKey, () -> {
            logger.info("从数据库查询用户列表");
            return userService.getUserList();
        });
    }

    /**
     * 根据状态获取用户列表（使用RedisCache工具类）
     * 
     * @param status 用户状态
     * @return 用户列表
     */
    @GetMapping("/list/status/{status}")
    public List<Map<String, Object>> getUserListByStatus(@PathVariable String status) {
        String cacheKey = cacheKeyUtil.generateKey("user", "listByStatus", status);
        logger.info("根据状态查询用户列表，状态: {}, 缓存Key: {}", status, cacheKey);
        
        // 根据状态设置不同的缓存时间
        long ttl = "active".equals(status) ? 3600 : 1800;
        
        return redisCache.getOrSet(cacheKey, () -> {
            logger.info("从数据库查询用户列表，状态: {}", status);
            return userService.getUserListByStatus(status);
        }, ttl);
    }

    /**
     * 分页获取用户列表（使用RedisCache工具类）
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 用户列表
     */
    @GetMapping("/list/page")
    public List<Map<String, Object>> getUserListByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        String cacheKey = cacheKeyUtil.generatePageKey("user", "listByPage", pageNum, pageSize);
        logger.info("分页查询用户列表，页码: {}, 页大小: {}, 缓存Key: {}", pageNum, pageSize, cacheKey);
        
        return redisCache.getOrSet(cacheKey, () -> {
            logger.info("从数据库查询用户列表，页码: {}, 页大小: {}", pageNum, pageSize);
            return userService.getUserListByPage(pageNum, pageSize);
        });
    }

    /**
     * 根据用户ID获取用户详情（使用RedisCache工具类）
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    @GetMapping("/{userId}")
    public List<Map<String, Object>> getUserById(@PathVariable Long userId) {
        String cacheKey = cacheKeyUtil.generateUserKey(userId, "user", "detail");
        logger.info("查询用户详情，用户ID: {}, 缓存Key: {}", userId, cacheKey);
        
        // 用户详情缓存时间较长
        return redisCache.getOrSet(cacheKey, () -> {
            logger.info("从数据库查询用户详情，用户ID: {}", userId);
            return userService.getUserById(userId);
        }, 7200); // 2小时
    }

    /**
     * 清除指定用户的缓存
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/cache/{userId}")
    public Map<String, Object> clearUserCache(@PathVariable Long userId) {
        String userDetailKey = cacheKeyUtil.generateUserKey(userId, "user", "detail");
        boolean deleted = redisCache.delete(userDetailKey);
        
        logger.info("清除用户缓存，用户ID: {}, 缓存Key: {}, 删除结果: {}", userId, userDetailKey, deleted);
        
        return Map.of(
            "success", true,
            "message", "用户缓存清除" + (deleted ? "成功" : "失败"),
            "userId", userId,
            "deleted", deleted
        );
    }

    /**
     * 清除所有用户相关缓存
     * 
     * @return 操作结果
     */
    @DeleteMapping("/cache/all")
    public Map<String, Object> clearAllUserCache() {
        String pattern = "ruoyi:cache:user:*";
        long deletedCount = redisCache.deleteByPattern(pattern);
        
        logger.info("清除所有用户缓存，匹配模式: {}, 删除数量: {}", pattern, deletedCount);
        
        return Map.of(
            "success", true,
            "message", "所有用户缓存清除成功",
            "deletedCount", deletedCount
        );
    }

    /**
     * 预热缓存 - 提前加载常用数据到缓存
     * 
     * @return 操作结果
     */
    @PostMapping("/cache/warmup")
    public Map<String, Object> warmupCache() {
        try {
            logger.info("开始预热缓存");
            
            // 预热用户列表
            getUserList();
            
            // 预热活跃用户列表
            getUserListByStatus("active");
            
            // 预热第一页数据
            getUserListByPage(1, 10);
            
            // 预热前几个用户的详情
            for (long i = 1; i <= 5; i++) {
                getUserById(i);
            }
            
            logger.info("缓存预热完成");
            
            return Map.of(
                "success", true,
                "message", "缓存预热完成"
            );
        } catch (Exception e) {
            logger.error("缓存预热异常，错误信息: {}", e.getMessage(), e);
            return Map.of(
                "success", false,
                "message", "缓存预热失败: " + e.getMessage()
            );
        }
    }
}
