package com.ruoyi.service;

import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户服务类（模拟业务逻辑）
 * 
 * <AUTHOR>
 */
@Service
public class UserService {

    /**
     * 模拟获取用户列表
     * 
     * @return 用户列表
     */
    public List<Map<String, Object>> getUserList() {
        // 模拟数据库查询延迟
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        List<Map<String, Object>> userList = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", i);
            user.put("username", "user" + i);
            user.put("email", "user" + i + "@example.com");
            user.put("createTime", new Date());
            user.put("status", i % 2 == 0 ? "active" : "inactive");
            userList.add(user);
        }
        return userList;
    }

    /**
     * 根据状态获取用户列表
     * 
     * @param status 用户状态
     * @return 用户列表
     */
    public List<Map<String, Object>> getUserListByStatus(String status) {
        // 模拟数据库查询延迟
        try {
            Thread.sleep(800);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        List<Map<String, Object>> userList = new ArrayList<>();
        for (int i = 1; i <= 20; i++) {
            String userStatus = i % 2 == 0 ? "active" : "inactive";
            if (status == null || status.equals(userStatus)) {
                Map<String, Object> user = new HashMap<>();
                user.put("id", i);
                user.put("username", "user" + i);
                user.put("email", "user" + i + "@example.com");
                user.put("createTime", new Date());
                user.put("status", userStatus);
                userList.add(user);
            }
        }
        return userList;
    }

    /**
     * 分页获取用户列表
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 用户列表
     */
    public List<Map<String, Object>> getUserListByPage(Integer pageNum, Integer pageSize) {
        // 模拟数据库查询延迟
        try {
            Thread.sleep(600);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        List<Map<String, Object>> userList = new ArrayList<>();
        int start = (pageNum - 1) * pageSize + 1;
        int end = pageNum * pageSize;
        
        for (int i = start; i <= end; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", i);
            user.put("username", "user" + i);
            user.put("email", "user" + i + "@example.com");
            user.put("createTime", new Date());
            user.put("status", i % 2 == 0 ? "active" : "inactive");
            userList.add(user);
        }
        return userList;
    }

    /**
     * 根据用户ID获取用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    public List<Map<String, Object>> getUserById(Long userId) {
        // 模拟数据库查询延迟
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> user = new HashMap<>();
        user.put("id", userId);
        user.put("username", "user" + userId);
        user.put("email", "user" + userId + "@example.com");
        user.put("createTime", new Date());
        user.put("status", userId % 2 == 0 ? "active" : "inactive");
        user.put("phone", "1380000" + String.format("%04d", userId));
        user.put("address", "北京市朝阳区" + userId + "号");
        result.add(user);
        return result;
    }
}
