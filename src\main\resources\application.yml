server:
  port: 8080

spring:
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: # 如果Redis设置了密码，请在此配置
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# 自定义缓存配置
cache:
  redis:
    # 默认缓存过期时间（秒）
    default-ttl: 3600
    # 缓存key前缀
    key-prefix: "ruoyi:cache:"
    # 是否启用缓存
    enabled: true

# 日志配置
logging:
  level:
    com.ruoyi: DEBUG
    org.springframework.data.redis: DEBUG
