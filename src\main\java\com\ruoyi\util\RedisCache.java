package com.ruoyi.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.config.CacheProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis缓存工具类
 * 专门用于处理List<Map<String, Object>>类型的缓存操作
 * 
 * <AUTHOR>
 */
@Component
public class RedisCache {

    private static final Logger logger = LoggerFactory.getLogger(RedisCache.class);

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CacheProperties cacheProperties;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 获取缓存数据，如果不存在则执行supplier并缓存结果
     * 
     * @param key 缓存key
     * @param supplier 数据提供者
     * @return 数据
     */
    public List<Map<String, Object>> getOrSet(String key, Supplier<List<Map<String, Object>>> supplier) {
        return getOrSet(key, supplier, cacheProperties.getDefaultTtl());
    }

    /**
     * 获取缓存数据，如果不存在则执行supplier并缓存结果
     * 
     * @param key 缓存key
     * @param supplier 数据提供者
     * @param ttl 缓存过期时间（秒）
     * @return 数据
     */
    public List<Map<String, Object>> getOrSet(String key, Supplier<List<Map<String, Object>>> supplier, long ttl) {
        if (!cacheProperties.getEnabled()) {
            logger.debug("缓存未启用，直接执行业务逻辑");
            return supplier.get();
        }

        try {
            // 先从缓存获取
            Object cachedData = redisTemplate.opsForValue().get(key);
            if (cachedData != null) {
                logger.debug("从缓存中获取到数据，Key: {}", key);
                return objectMapper.convertValue(cachedData, new TypeReference<List<Map<String, Object>>>() {});
            }

            // 缓存不存在，执行业务逻辑
            logger.debug("缓存中没有数据，执行业务逻辑，Key: {}", key);
            List<Map<String, Object>> data = supplier.get();

            // 存入缓存
            if (data != null) {
                redisTemplate.opsForValue().set(key, data, ttl, TimeUnit.SECONDS);
                logger.debug("数据已存入缓存，Key: {}, TTL: {}秒", key, ttl);
            }

            return data;

        } catch (Exception e) {
            logger.error("缓存操作异常，Key: {}, 错误信息: {}", key, e.getMessage(), e);
            // 缓存异常时直接执行业务逻辑
            return supplier.get();
        }
    }

    /**
     * 获取缓存数据
     * 
     * @param key 缓存key
     * @return 缓存数据，不存在返回null
     */
    public List<Map<String, Object>> get(String key) {
        if (!cacheProperties.getEnabled()) {
            return null;
        }

        try {
            Object cachedData = redisTemplate.opsForValue().get(key);
            if (cachedData != null) {
                return objectMapper.convertValue(cachedData, new TypeReference<List<Map<String, Object>>>() {});
            }
        } catch (Exception e) {
            logger.error("获取缓存数据异常，Key: {}, 错误信息: {}", key, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 设置缓存数据
     * 
     * @param key 缓存key
     * @param data 数据
     */
    public void set(String key, List<Map<String, Object>> data) {
        set(key, data, cacheProperties.getDefaultTtl());
    }

    /**
     * 设置缓存数据
     * 
     * @param key 缓存key
     * @param data 数据
     * @param ttl 过期时间（秒）
     */
    public void set(String key, List<Map<String, Object>> data, long ttl) {
        if (!cacheProperties.getEnabled() || data == null) {
            return;
        }

        try {
            redisTemplate.opsForValue().set(key, data, ttl, TimeUnit.SECONDS);
            logger.debug("数据已存入缓存，Key: {}, TTL: {}秒", key, ttl);
        } catch (Exception e) {
            logger.error("设置缓存数据异常，Key: {}, 错误信息: {}", key, e.getMessage(), e);
        }
    }

    /**
     * 删除缓存
     * 
     * @param key 缓存key
     * @return 是否删除成功
     */
    public boolean delete(String key) {
        if (!cacheProperties.getEnabled()) {
            return false;
        }

        try {
            Boolean result = redisTemplate.delete(key);
            logger.debug("删除缓存，Key: {}, 结果: {}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("删除缓存异常，Key: {}, 错误信息: {}", key, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量删除缓存
     * 
     * @param keys 缓存key集合
     * @return 删除的数量
     */
    public long delete(Set<String> keys) {
        if (!cacheProperties.getEnabled() || keys == null || keys.isEmpty()) {
            return 0;
        }

        try {
            Long result = redisTemplate.delete(keys);
            logger.debug("批量删除缓存，Keys: {}, 删除数量: {}", keys, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("批量删除缓存异常，Keys: {}, 错误信息: {}", keys, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 根据模式删除缓存
     * 
     * @param pattern 匹配模式
     * @return 删除的数量
     */
    public long deleteByPattern(String pattern) {
        if (!cacheProperties.getEnabled()) {
            return 0;
        }

        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                return delete(keys);
            }
            return 0;
        } catch (Exception e) {
            logger.error("根据模式删除缓存异常，Pattern: {}, 错误信息: {}", pattern, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存key
     * @return 是否存在
     */
    public boolean exists(String key) {
        if (!cacheProperties.getEnabled()) {
            return false;
        }

        try {
            Boolean result = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("检查缓存是否存在异常，Key: {}, 错误信息: {}", key, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取缓存过期时间
     * 
     * @param key 缓存key
     * @return 过期时间（秒），-1表示永不过期，-2表示key不存在
     */
    public long getExpire(String key) {
        if (!cacheProperties.getEnabled()) {
            return -2;
        }

        try {
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            logger.error("获取缓存过期时间异常，Key: {}, 错误信息: {}", key, e.getMessage(), e);
            return -2;
        }
    }

    /**
     * 设置缓存过期时间
     * 
     * @param key 缓存key
     * @param ttl 过期时间（秒）
     * @return 是否设置成功
     */
    public boolean expire(String key, long ttl) {
        if (!cacheProperties.getEnabled()) {
            return false;
        }

        try {
            Boolean result = redisTemplate.expire(key, ttl, TimeUnit.SECONDS);
            logger.debug("设置缓存过期时间，Key: {}, TTL: {}秒, 结果: {}", key, ttl, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("设置缓存过期时间异常，Key: {}, TTL: {}秒, 错误信息: {}", key, ttl, e.getMessage(), e);
            return false;
        }
    }
}
